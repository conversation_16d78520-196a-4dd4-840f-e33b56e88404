import pandas as pd
import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import os
from config import DB_CONFIG, EXCEL_FILE

def connect_to_database():
    """连接到MySQL数据库"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"连接数据库时出错: {e}")
        return None

def create_table(connection):
    """创建数据表"""
    try:
        cursor = connection.cursor()
        
        # 先删除旧表
        drop_table_query = "DROP TABLE IF EXISTS excel_data"
        cursor.execute(drop_table_query)
        connection.commit()
        print("旧表已删除")
        
        # 创建新表
        create_table_query = """
        CREATE TABLE excel_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            number INT,
            value INT DEFAULT 0
        )
        """
        cursor.execute(create_table_query)
        connection.commit()
        print("新表创建成功")
        return True
    except Error as e:
        print(f"创建表时出错: {e}")
        return False

def read_excel_file(file_path):
    """读取Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件，数据形状: {df.shape}")
        print("列名:", df.columns.tolist())
        print("行名:", df.index.tolist())
        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def insert_data_to_db(connection, df):
    """将数据插入到数据库"""
    try:
        cursor = connection.cursor()
        
        # 获取列名和行名
        columns = df.columns.tolist()
        
        # 准备插入语句
        insert_query = """
        INSERT INTO excel_data (name, number, value) 
        VALUES (%s, %s, %s)
        """
        
        # 遍历数据并插入
        for col_idx, col_name in enumerate(columns):
            # 跳过第一列（Unnamed: 0），因为它包含武器名称
            if col_name == 'Unnamed: 0':
                continue
                
            for row_idx in range(len(df)):
                # 获取单元格的值
                cell_value = df.iloc[row_idx, col_idx]
                
                # 将NaN值转换为0
                if pd.isna(cell_value):
                    cell_value = 0
                
                # 获取武器名称（第一列的值）
                weapon_name = df.iloc[row_idx, 0]  # 第一列是武器名
                
                # 跳过武器名为NaN的情况
                if pd.isna(weapon_name):
                    continue
                
                # 创建name字段：皮肤名-武器名
                name = f"{col_name}-{weapon_name}"
                
                # 尝试转换为整数，如果失败则跳过
                try:
                    if isinstance(cell_value, (int, float)):
                        number_value = int(cell_value)
                    elif isinstance(cell_value, str):
                        # 尝试转换为整数
                        number_value = int(float(cell_value))
                    else:
                        # 跳过非数值数据
                        print(f"跳过非数值数据: {name} = {cell_value}")
                        continue
                except (ValueError, TypeError):
                    # 跳过无法转换为数值的数据
                    print(f"跳过无法转换的数据: {name} = {cell_value}")
                    continue
                
                # 插入数据
                cursor.execute(insert_query, (name, number_value, 0))
        
        connection.commit()
        print("数据插入成功")
        return True
    except Error as e:
        print(f"插入数据时出错: {e}")
        return False

def main():
    """主函数"""
    # 检查文件是否存在
    if not os.path.exists(EXCEL_FILE):
        print(f"错误：找不到文件 {EXCEL_FILE}")
        return
    
    # 连接数据库
    print("正在连接数据库...")
    connection = connect_to_database()
    if not connection:
        return
    
    # 创建表
    print("正在创建数据表...")
    if not create_table(connection):
        connection.close()
        return
    
    # 读取Excel文件
    print("正在读取Excel文件...")
    df = read_excel_file(EXCEL_FILE)
    if df is None:
        connection.close()
        return
    
    # 插入数据
    print("正在插入数据到数据库...")
    if insert_data_to_db(connection, df):
        print("数据导入完成！")
    else:
        print("数据导入失败！")
    
    # 关闭连接
    connection.close()

if __name__ == "__main__":
    main() 