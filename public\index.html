<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YHTJ交易系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            display: flex;
            gap: 20px;
            justify-content: center;
            background: #f8f9fa;
        }

        .btn {
            padding: 15px 40px;
            font-size: 1.2em;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-buy {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .btn-buy:hover {
            background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .btn-sell {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
            color: white;
        }

        .btn-sell:hover {
            background: linear-gradient(135deg, #da190b 0%, #f44336 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }

        .data-section {
            padding: 30px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .data-table tr:hover {
            background: #f5f5f5;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 15px;
            width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            color: #f44336;
            margin-top: 10px;
        }

        .success {
            color: #4CAF50;
            margin-top: 10px;
        }

        .toast-notification {
            position: fixed;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
            font-size: 1.2em;
            font-weight: bold;
            z-index: 10000;
            opacity: 1;
            transition: all 0.5s ease;
        }

        .toast-notification.fade-out {
            opacity: 0;
            transform: translateX(-50%) translateY(20px);
        }

        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 5px;
            background: white;
        }

        .search-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .search-item:hover {
            background-color: #f5f5f5;
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .search-item .item-name {
            font-weight: bold;
            color: #333;
        }

        .search-item .item-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>YHTJ交易系统</h1>
            <p>武器皮肤交易平台</p>
        </div>

        <div class="controls">
            <button class="btn btn-buy" onclick="openBuyModal()">购买</button>
            <button class="btn btn-sell" onclick="openSellModal()">出售</button>
        </div>

        <div class="data-section">
            <h2>库存数据</h2>
            <div id="loading" class="loading">正在加载数据...</div>
            <table class="data-table" id="dataTable" style="display: none;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>商品名称</th>
                        <th>数量</th>
                        <th>价值</th>
                    </tr>
                </thead>
                <tbody id="dataBody">
                </tbody>
            </table>
        </div>
    </div>

    <!-- 购买模态框 -->
    <div id="buyModal" class="modal">
        <div class="modal-content">
            <h2>购买商品</h2>
            <div class="form-group">
                <label for="buyName">商品名称:</label>
                <input type="text" id="buyName" placeholder="请输入商品名称，如：君权帝王m4" oninput="searchItems('buy')">
                <div id="buySearchResults" class="search-results"></div>
            </div>
            <div class="form-group">
                <label for="buyQuantity">购买数量:</label>
                <input type="number" id="buyQuantity" placeholder="请输入购买数量">
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closeBuyModal()">取消</button>
                <button class="btn btn-buy" onclick="buyItem()">确认购买</button>
            </div>
            <div id="buyMessage"></div>
        </div>
    </div>

    <!-- 出售模态框 -->
    <div id="sellModal" class="modal">
        <div class="modal-content">
            <h2>出售商品</h2>
            <div class="form-group">
                <label for="sellName">商品名称:</label>
                <input type="text" id="sellName" placeholder="请输入商品名称，如：君权帝王m4" oninput="searchItems('sell')">
                <div id="sellSearchResults" class="search-results"></div>
            </div>
            <div class="form-group">
                <label for="sellQuantity">出售数量:</label>
                <input type="number" id="sellQuantity" placeholder="请输入出售数量">
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary" onclick="closeSellModal()">取消</button>
                <button class="btn btn-sell" onclick="sellItem()">确认出售</button>
            </div>
            <div id="sellMessage"></div>
        </div>
    </div>

    <script>
        let selectedItem = null;
        let currentAction = null;

        // 加载数据
        function loadData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('dataBody');
                    tbody.innerHTML = '';
                    
                    data.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${item.id}</td>
                            <td>${item.name}</td>
                            <td>${item.number}</td>
                            <td>${item.value}</td>
                        `;
                        // 添加点击事件
                        row.onclick = () => selectItem(item);
                        tbody.appendChild(row);
                    });
                    
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('dataTable').style.display = 'table';
                })
                .catch(error => {
                    console.error('加载数据失败:', error);
                    document.getElementById('loading').textContent = '加载数据失败';
                });
        }

        // 打开购买模态框
        function openBuyModal() {
            document.getElementById('buyModal').style.display = 'block';
            document.getElementById('buyMessage').innerHTML = '';
            // 清空输入框
            document.getElementById('buyName').value = '';
            document.getElementById('buyQuantity').value = '';
            document.getElementById('buySearchResults').innerHTML = '';
        }

        // 关闭购买模态框
        function closeBuyModal() {
            document.getElementById('buyModal').style.display = 'none';
        }

        // 打开出售模态框
        function openSellModal() {
            document.getElementById('sellModal').style.display = 'block';
            document.getElementById('sellMessage').innerHTML = '';
            // 清空输入框
            document.getElementById('sellName').value = '';
            document.getElementById('sellQuantity').value = '';
            document.getElementById('sellSearchResults').innerHTML = '';
        }

        // 关闭出售模态框
        function closeSellModal() {
            document.getElementById('sellModal').style.display = 'none';
        }

        // 搜索商品
        function searchItems(type) {
            const input = document.getElementById(type + 'Name');
            const searchTerm = input.value.trim();
            const resultsDiv = document.getElementById(type + 'SearchResults');
            
            if (searchTerm.length < 2) {
                resultsDiv.innerHTML = '';
                return;
            }
            
            fetch(`/api/search?q=${encodeURIComponent(searchTerm)}`)
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '';
                    
                    if (data.length === 0) {
                        resultsDiv.innerHTML = '<div class="search-item">没有找到匹配的商品</div>';
                        return;
                    }
                    
                    data.forEach(item => {
                        const div = document.createElement('div');
                        div.className = 'search-item';
                        div.innerHTML = `
                            <div class="item-name">${item.name}</div>
                            <div class="item-details">库存: ${item.number} | ID: ${item.id}</div>
                        `;
                        div.onclick = () => selectItem(type, item.name);
                        resultsDiv.appendChild(div);
                    });
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                    resultsDiv.innerHTML = '<div class="search-item">搜索失败</div>';
                });
        }

        // 选择商品
        function selectItem(item) {
            selectedItem = item;
            document.getElementById('selectedItemName').textContent = item.name;
            document.getElementById('selectedItemStock').textContent = item.number;
            document.getElementById('actionModal').style.display = 'block';
            document.getElementById('quantitySection').style.display = 'none';
            document.getElementById('actionMessage').innerHTML = '';
        }

        // 显示数量输入
        function showQuantityInput(action) {
            currentAction = action;
            document.getElementById('quantitySection').style.display = 'block';
            document.getElementById('operationType').textContent = action === 'buy' ? '购买数量' : '出售数量';
            document.getElementById('quantityInput').value = '';
            document.getElementById('quantityInput').focus();
            
            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.className = action === 'buy' ? 'btn btn-buy' : 'btn btn-sell';
            confirmBtn.textContent = action === 'buy' ? '确认购买' : '确认出售';
        }

        // 执行操作
        function executeAction() {
            const quantity = document.getElementById('quantityInput').value;
            
            if (!quantity || quantity <= 0) {
                document.getElementById('actionMessage').innerHTML = '<div class="error">请输入有效数量</div>';
                return;
            }

            const endpoint = currentAction === 'buy' ? '/api/buy' : '/api/sell';
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    name: selectedItem.name, 
                    quantity: parseInt(quantity) 
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('actionMessage').innerHTML = `<div class="error">${data.error}</div>`;
                } else {
                    showToast(data.message, 'success');
                    closeActionModal();
                    loadData();
                }
            })
            .catch(error => {
                console.error('操作失败:', error);
                document.getElementById('actionMessage').innerHTML = '<div class="error">操作失败，请重试</div>';
            });
        }

        // 关闭操作模态框
        function closeActionModal() {
            document.getElementById('actionModal').style.display = 'none';
            selectedItem = null;
            currentAction = null;
        }

        // 显示toast通知
        function showToast(message, type = 'success') {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.textContent = message;
            
            // 根据类型设置颜色
            if (type === 'error') {
                toast.style.background = 'linear-gradient(135deg, #f44336 0%, #da190b 100%)';
                toast.style.boxShadow = '0 5px 15px rgba(244, 67, 54, 0.4)';
            }
            
            document.body.appendChild(toast);

            // 2秒后开始淡出
            setTimeout(() => {
                toast.classList.add('fade-out');
                // 淡出动画完成后移除元素
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 500);
            }, 2000);
        }

        // 购买商品
        function buyItem() {
            const name = document.getElementById('buyName').value;
            const quantity = document.getElementById('buyQuantity').value;
            
            if (!name || !quantity) {
                document.getElementById('buyMessage').innerHTML = '<div class="error">请填写完整信息</div>';
                return;
            }

            fetch('/api/buy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ name, quantity: parseInt(quantity) })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('buyMessage').innerHTML = `<div class="error">${data.error}</div>`;
                } else {
                    showToast(data.message, 'success');
                    closeBuyModal();
                    loadData();
                }
            })
            .catch(error => {
                console.error('购买失败:', error);
                document.getElementById('buyMessage').innerHTML = '<div class="error">购买失败，请重试</div>';
            });
        }

        // 出售商品
        function sellItem() {
            const name = document.getElementById('sellName').value;
            const quantity = document.getElementById('sellQuantity').value;
            
            if (!name || !quantity) {
                document.getElementById('sellMessage').innerHTML = '<div class="error">请填写完整信息</div>';
                return;
            }

            fetch('/api/sell', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ name, quantity: parseInt(quantity) })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('sellMessage').innerHTML = `<div class="error">${data.error}</div>`;
                } else {
                    showToast(data.message, 'success');
                    closeSellModal();
                    loadData();
                }
            })
            .catch(error => {
                console.error('出售失败:', error);
                document.getElementById('sellMessage').innerHTML = '<div class="error">出售失败，请重试</div>';
            });
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const buyModal = document.getElementById('buyModal');
            const sellModal = document.getElementById('sellModal');
            const actionModal = document.getElementById('actionModal');
            
            if (event.target === buyModal) {
                closeBuyModal();
            }
            if (event.target === sellModal) {
                closeSellModal();
            }
            if (event.target === actionModal) {
                closeActionModal();
            }
        }

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html> 
