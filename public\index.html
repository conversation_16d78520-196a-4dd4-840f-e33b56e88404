<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YHTJ交易系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 1em;
            opacity: 0.8;
        }

        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            padding: 20px;
        }

        .item-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .item-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
        }

        .item-name {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            word-break: break-word;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 20% auto;
            padding: 30px;
            border-radius: 10px;
            width: 350px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            text-align: center;
        }

        .modal h2 {
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 1.5em;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 25px;
            font-size: 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-buy {
            background: #27ae60;
            color: white;
        }

        .btn-buy:hover {
            background: #229954;
            transform: translateY(-1px);
        }

        .btn-sell {
            background: #e74c3c;
            color: white;
        }

        .btn-sell:hover {
            background: #c0392b;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .quantity-section {
            display: none;
            margin-top: 20px;
        }

        .quantity-section input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 15px;
            text-align: center;
        }

        .quantity-section input:focus {
            outline: none;
            border-color: #3498db;
        }

        .error {
            color: #e74c3c;
            margin-top: 10px;
            font-size: 14px;
        }

        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            opacity: 1;
            transition: all 0.3s ease;
        }

        .toast-notification.error {
            background: #e74c3c;
        }

        .toast-notification.fade-out {
            opacity: 0;
            transform: translateX(100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>YHTJ交易系统</h1>
            <p>武器皮肤交易平台</p>
        </div>

        <div id="loading" class="loading">正在加载数据...</div>
        <div id="itemsGrid" class="items-grid" style="display: none;"></div>
    </div>

    <!-- 操作模态框 -->
    <div id="actionModal" class="modal">
        <div class="modal-content">
            <h2 id="selectedItemName">选择操作</h2>

            <div class="action-buttons">
                <button class="btn btn-buy" onclick="showQuantityInput('buy')">购买</button>
                <button class="btn btn-sell" onclick="showQuantityInput('sell')">出售</button>
            </div>

            <div id="quantitySection" class="quantity-section">
                <input type="number" id="quantityInput" placeholder="请输入数量" min="1">
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button class="btn btn-secondary" onclick="closeActionModal()">取消</button>
                    <button id="confirmBtn" class="btn" onclick="executeAction()">确认</button>
                </div>
            </div>

            <div id="actionMessage"></div>
        </div>
    </div>

    <script>
        let selectedItem = null;
        let currentAction = null;

        // 加载数据
        function loadData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    const grid = document.getElementById('itemsGrid');
                    grid.innerHTML = '';

                    data.forEach(item => {
                        const card = document.createElement('div');
                        card.className = 'item-card';
                        card.innerHTML = `<div class="item-name">${item.name}</div>`;
                        card.onclick = () => selectItem(item);
                        grid.appendChild(card);
                    });

                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('itemsGrid').style.display = 'grid';
                })
                .catch(error => {
                    console.error('加载数据失败:', error);
                    document.getElementById('loading').textContent = '加载数据失败';
                });
        }

        // 选择商品
        function selectItem(item) {
            selectedItem = item;
            document.getElementById('selectedItemName').textContent = item.name;
            document.getElementById('actionModal').style.display = 'block';
            document.getElementById('quantitySection').style.display = 'none';
            document.getElementById('actionMessage').innerHTML = '';
        }

        // 显示数量输入
        function showQuantityInput(action) {
            currentAction = action;
            document.getElementById('quantitySection').style.display = 'block';
            document.getElementById('quantityInput').value = '';
            document.getElementById('quantityInput').focus();

            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.className = action === 'buy' ? 'btn btn-buy' : 'btn btn-sell';
            confirmBtn.textContent = action === 'buy' ? '确认购买' : '确认出售';
        }

        // 执行操作
        function executeAction() {
            const quantity = document.getElementById('quantityInput').value;

            if (!quantity || quantity <= 0) {
                document.getElementById('actionMessage').innerHTML = '<div class="error">请输入有效数量</div>';
                return;
            }

            const endpoint = currentAction === 'buy' ? '/api/buy' : '/api/sell';

            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: selectedItem.name,
                    quantity: parseInt(quantity)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('actionMessage').innerHTML = `<div class="error">${data.error}</div>`;
                } else {
                    showToast(data.message, 'success');
                    closeActionModal();
                    loadData();
                }
            })
            .catch(error => {
                console.error('操作失败:', error);
                document.getElementById('actionMessage').innerHTML = '<div class="error">操作失败，请重试</div>';
            });
        }

        // 关闭操作模态框
        function closeActionModal() {
            document.getElementById('actionModal').style.display = 'none';
            selectedItem = null;
            currentAction = null;
        }

        // 显示toast通知
        function showToast(message, type = 'success') {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            if (type === 'error') {
                toast.classList.add('error');
            }
            toast.textContent = message;

            document.body.appendChild(toast);

            // 3秒后开始淡出
            setTimeout(() => {
                toast.classList.add('fade-out');
                // 淡出动画完成后移除元素
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }, 3000);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const actionModal = document.getElementById('actionModal');

            if (event.target === actionModal) {
                closeActionModal();
            }
        }

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
