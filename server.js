const express = require('express');
const mysql = require('mysql2');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 数据库连接配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '123456', // 请根据你的MySQL密码修改
    database: 'YHTJ'
};

// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

connection.connect((err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        return;
    }
    console.log('数据库连接成功');
});

// 路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 获取所有数据
app.get('/api/data', (req, res) => {
    const query = 'SELECT * FROM excel_data';
    connection.query(query, (err, results) => {
        if (err) {
            console.error('查询失败:', err);
            res.status(500).json({ error: '查询失败' });
            return;
        }
        res.json(results);
    });
});

// 搜索喷漆名称
app.get('/api/search', (req, res) => {
    const searchTerm = req.query.q;
    if (!searchTerm) {
        res.json([]);
        return;
    }

    // 将搜索词转换为小写，并移除可能的"-"字符
    const cleanSearchTerm = searchTerm.toLowerCase().replace(/-/g, '');
    
    // 使用更灵活的搜索模式
    const query = `
        SELECT * FROM excel_data 
        WHERE LOWER(REPLACE(name, '-', '')) LIKE ? 
        OR LOWER(name) LIKE ?
        ORDER BY 
            CASE 
                WHEN LOWER(REPLACE(name, '-', '')) = ? THEN 1
                WHEN LOWER(REPLACE(name, '-', '')) LIKE ? THEN 2
                WHEN LOWER(name) LIKE ? THEN 3
                ELSE 4
            END,
            name
        LIMIT 10
    `;
    
    const exactMatch = cleanSearchTerm;
    const startsWith = cleanSearchTerm + '%';
    const contains = '%' + cleanSearchTerm + '%';
    const nameContains = '%' + searchTerm.toLowerCase() + '%';
    
    connection.query(query, [contains, nameContains, exactMatch, startsWith, nameContains], (err, results) => {
        if (err) {
            console.error('搜索失败:', err);
            res.status(500).json({ error: '搜索失败' });
            return;
        }
        res.json(results);
    });
});

// 购买接口 - 用户购买商品，用户库存增加
app.post('/api/buy', (req, res) => {
    const { name, quantity } = req.body;
    
    if (!name || !quantity) {
        res.status(400).json({ error: '缺少必要参数' });
        return;
    }
    
    const query = 'UPDATE excel_data SET number = number + ? WHERE LOWER(name) = LOWER(?)';
    connection.query(query, [quantity, name], (err, result) => {
        if (err) {
            console.error('购买失败:', err);
            res.status(500).json({ error: '购买失败' });
            return;
        }
        
        if (result.affectedRows === 0) {
            res.status(400).json({ error: '商品不存在' });
            return;
        }
        
        res.json({ message: '购买成功', affectedRows: result.affectedRows });
    });
});

// 出售接口 - 用户出售商品给系统，用户库存减少
app.post('/api/sell', (req, res) => {
    const { name, quantity } = req.body;
    
    if (!name || !quantity) {
        res.status(400).json({ error: '缺少必要参数' });
        return;
    }
    
    const query = 'UPDATE excel_data SET number = number - ? WHERE LOWER(name) = LOWER(?) AND number >= ?';
    connection.query(query, [quantity, name, quantity], (err, result) => {
        if (err) {
            console.error('出售失败:', err);
            res.status(500).json({ error: '出售失败' });
            return;
        }
        
        if (result.affectedRows === 0) {
            res.status(400).json({ error: '库存不足或商品不存在' });
            return;
        }
        
        res.json({ message: '出售成功', affectedRows: result.affectedRows });
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
}); 