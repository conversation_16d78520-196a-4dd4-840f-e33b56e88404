const express = require('express');
const mysql = require('mysql2');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 数据库连接配置
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '123456', // 请根据你的MySQL密码修改
    database: 'YHTJ'
};

// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

connection.connect((err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        return;
    }
    console.log('数据库连接成功');
});

// 路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 获取所有数据，按后缀字段分组排序
app.get('/api/data', (req, res) => {
    const query = 'SELECT * FROM excel_data ORDER BY name';
    connection.query(query, (err, results) => {
        if (err) {
            console.error('查询失败:', err);
            res.status(500).json({ error: '查询失败' });
            return;
        }

        // 定义后缀的排序顺序（根据Excel中的行顺序）
        const suffixOrder = [
            '通用', 'M4A1', 'AKM', 'MPX', 'M700', 'KC556', 'DR-10', 'B-11SASS',
            'CAR-58', 'DTX17', 'MP7', 'VSS', 'P90', 'MC1', 'SR-18', 'Vector',
            'L85A1', 'MK16-L', 'G36', 'GRA192', '毛瑟', '10', '11SASS', '12',
            '16', '18', '58', '75', '88', '95'
        ];

        // 按后缀字段分组
        const groupedData = {};

        results.forEach(item => {
            // 提取后缀（格式是 "前缀-后缀"）
            const parts = item.name.split('-');
            const suffix = parts.length > 1 ? parts[parts.length - 1] : item.name;

            if (!groupedData[suffix]) {
                groupedData[suffix] = [];
            }
            groupedData[suffix].push(item);
        });

        // 按预定义的后缀顺序重新组织数据
        const sortedResults = [];
        suffixOrder.forEach(suffix => {
            if (groupedData[suffix]) {
                // 同一后缀内按名称排序
                groupedData[suffix].sort((a, b) => a.name.localeCompare(b.name));
                sortedResults.push(...groupedData[suffix]);
            }
        });

        // 添加任何未在预定义列表中的后缀
        Object.keys(groupedData).forEach(suffix => {
            if (!suffixOrder.includes(suffix)) {
                groupedData[suffix].sort((a, b) => a.name.localeCompare(b.name));
                sortedResults.push(...groupedData[suffix]);
            }
        });

        res.json(sortedResults);
    });
});



// 购买接口 - 用户购买商品，用户库存增加
app.post('/api/buy', (req, res) => {
    const { name, quantity } = req.body;
    
    if (!name || !quantity) {
        res.status(400).json({ error: '缺少必要参数' });
        return;
    }
    
    const query = 'UPDATE excel_data SET number = number + ? WHERE LOWER(name) = LOWER(?)';
    connection.query(query, [quantity, name], (err, result) => {
        if (err) {
            console.error('购买失败:', err);
            res.status(500).json({ error: '购买失败' });
            return;
        }
        
        if (result.affectedRows === 0) {
            res.status(400).json({ error: '商品不存在' });
            return;
        }
        
        res.json({ message: '购买成功', affectedRows: result.affectedRows });
    });
});

// 出售接口 - 用户出售商品给系统，用户库存减少
app.post('/api/sell', (req, res) => {
    const { name, quantity } = req.body;
    
    if (!name || !quantity) {
        res.status(400).json({ error: '缺少必要参数' });
        return;
    }
    
    const query = 'UPDATE excel_data SET number = number - ? WHERE LOWER(name) = LOWER(?) AND number >= ?';
    connection.query(query, [quantity, name, quantity], (err, result) => {
        if (err) {
            console.error('出售失败:', err);
            res.status(500).json({ error: '出售失败' });
            return;
        }
        
        if (result.affectedRows === 0) {
            res.status(400).json({ error: '库存不足或商品不存在' });
            return;
        }
        
        res.json({ message: '出售成功', affectedRows: result.affectedRows });
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});