# Excel数据导入MySQL数据库

这个脚本用于将Excel文件中的数据导入到MySQL数据库中。

## 功能说明

- 读取Excel文件（喷漆数量.xlsx）
- 自动创建数据表（excel_data）
- 将Excel数据转换为数据库格式：
  - id: 自动递增的主键
  - name: 列名-行名的组合
  - number: Excel单元格的数值
  - value: 默认值为0

## 环境准备

### 1. 创建Conda虚拟环境

```bash
conda create -n YHTJ python=3.9 
```

### 2. 激活虚拟环境

```bash
conda activate YHTJ
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

## 配置数据库连接

1. 编辑 `config.py` 文件
2. 修改数据库连接信息：
   ```python
   DB_CONFIG = {
       'host': 'localhost',
       'user': '你的用户名',
       'password': '你的密码',
       'database': 'YHTJ'
   }
   ```

## 运行脚本

```bash
python excel_to_db.py
```

## 数据表结构

```sql
CREATE TABLE excel_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    number DECIMAL(10,2),
    value INT DEFAULT 0
);
```

## 注意事项

1. 确保MySQL服务正在运行
2. 确保数据库YHTJ已创建
3. 确保Excel文件在同一目录下
4. 脚本会自动跳过空值（NaN）

## 错误排查

- 如果连接数据库失败，请检查config.py中的连接信息
- 如果读取Excel失败，请确保文件格式正确
- 如果插入数据失败，请检查数据库权限 